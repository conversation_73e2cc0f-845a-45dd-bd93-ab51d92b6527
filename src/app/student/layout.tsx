'use client';

import { usePathname } from 'next/navigation';
import StudentNavigation from '../../components/student/StudentNavigation';

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Don't show navigation on the main student homepage or auth pages
  const showNavigation = pathname &&
    !pathname.endsWith('/student') &&
    !pathname.includes('/auth/');

  return (
    <div className="student-portal min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {showNavigation && <StudentNavigation />}
      <main className={showNavigation ? 'pt-0' : ''}>
        {children}
      </main>
    </div>
  );
}
