export const metadata = {
  title: 'LanguageGems Student Portal',
  description: 'Master French, Spanish, and German with interactive games and exercises',
};

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This layout works within the main app layout structure
  // No need for separate HTML/body tags or AuthProvider (already provided by root layout)
  return (
    <div className="student-portal">
      {children}
    </div>
  );
}
