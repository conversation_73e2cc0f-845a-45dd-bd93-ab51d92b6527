"use client";

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '../../components/auth/AuthProvider';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function StudentHomepage() {
  const { user, userRole, isLoading } = useAuth();
  const router = useRouter();

  // If user is already authenticated, redirect to student dashboard
  useEffect(() => {
    if (!isLoading && user) {
      if (userRole === 'student') {
        router.push('/student-dashboard');
      } else {
        // Non-student users should be redirected to main site
        const mainSiteUrl = process.env.NODE_ENV === 'development'
          ? 'http://localhost:3000/account'
          : 'https://languagegems.com/account';
        window.location.href = mainSiteUrl;
      }
    }
  }, [user, userRole, isLoading, router]);

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-4 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl font-semibold">Loading your adventure...</p>
        </div>
      </div>
    );
  }

  // Show login interface for non-authenticated users
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-32 w-24 h-24 bg-yellow-300/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-blue-300/10 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 bg-green-300/20 rounded-full animate-bounce delay-500"></div>
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 max-w-md w-full border border-white/20"
        >
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-24 h-24 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
            >
              <span className="text-4xl">💎</span>
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-4xl font-bold text-gray-800 mb-3"
            >
              Welcome to LanguageGems!
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 text-lg"
            >
              Ready to master French, Spanish, or German? 🚀
            </motion.p>
          </div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-6 mb-8"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/auth/login"
                className="w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white py-4 px-6 rounded-xl font-semibold text-center block hover:shadow-xl transition-all text-lg shadow-lg"
              >
                🎮 Sign In & Play
              </Link>
            </motion.div>

            <div className="text-center py-2">
              <p className="text-gray-500 text-sm">Don't have an account yet?</p>
              <p className="text-gray-700 font-medium">Ask your teacher for your login details</p>
            </div>
          </motion.div>
          
          {/* Features Preview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="grid grid-cols-3 gap-4 mb-8"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🎮</span>
              </div>
              <p className="text-gray-600 text-sm font-medium">Fun Games</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🏆</span>
              </div>
              <p className="text-gray-600 text-sm font-medium">Earn Points</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">📈</span>
              </div>
              <p className="text-gray-600 text-sm font-medium">Track Progress</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="border-t border-gray-200 pt-6"
          >
            <div className="text-center">
              <p className="text-gray-500 text-sm mb-3">Are you a teacher?</p>
              <a
                href={process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://languagegems.com'}
                className="text-purple-600 hover:text-purple-700 font-medium transition-colors"
              >
                Visit our teacher portal →
              </a>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
